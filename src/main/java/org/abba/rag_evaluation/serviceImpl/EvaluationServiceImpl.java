package org.abba.rag_evaluation.serviceImpl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.abba.rag_evaluation.enums.EvaluationStatus;
import org.abba.rag_evaluation.enums.EvaluationType;
import org.abba.rag_evaluation.exception.EvaluationNotFoundException;
import org.abba.rag_evaluation.exception.TaskNotFoundException;
import org.abba.rag_evaluation.po.Evaluation;
import org.abba.rag_evaluation.po.Task;
import org.abba.rag_evaluation.repository.EvaluationRepository;
import org.abba.rag_evaluation.repository.TaskRepository;
import org.abba.rag_evaluation.service.EvaluationService;
import org.abba.rag_evaluation.util.PythonEvaluator;
import org.abba.rag_evaluation.vo.CustomEvalRequestVO;
import org.abba.rag_evaluation.vo.DeleteEvaluationResponseVO;
import org.abba.rag_evaluation.vo.EvaluationDetailVO;
import org.abba.rag_evaluation.vo.EvaluationListVO;
import org.abba.rag_evaluation.vo.EvaluationListVO.EvaluationVO;
import org.abba.rag_evaluation.vo.EvaluationResponseVO;
import org.abba.rag_evaluation.vo.EvaluationStatusVO;
import org.abba.rag_evaluation.vo.MultiTurnEvalRequestVO;
import org.abba.rag_evaluation.vo.SingleTurnEvalRequestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 评估服务实现类
 */
@Service
public class EvaluationServiceImpl implements EvaluationService {

    /**
     * 评估数据访问层
     */
    private final EvaluationRepository evaluationRepository;

    /**
     * 任务数据访问层
     */
    private final TaskRepository taskRepository;

    /**
     * Python评估器
     */
    private final PythonEvaluator pythonEvaluator;

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;

    /**
     * 构造函数注入
     *
     * @param evaluationRepository 评估数据访问层
     * @param taskRepository 任务数据访问层
     * @param pythonEvaluator Python评估器
     */
    @Autowired
    public EvaluationServiceImpl(EvaluationRepository evaluationRepository, TaskRepository taskRepository, PythonEvaluator pythonEvaluator) {
        this.evaluationRepository = evaluationRepository;
        this.taskRepository = taskRepository;
        this.pythonEvaluator = pythonEvaluator;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 获取任务下的所有评估
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 评估列表值对象
     */
    @Override
    public EvaluationListVO getAllEvaluations(Integer taskId, Integer userId) {
        // 验证任务是否存在且属于当前用户
        // Task task = taskRepository.findByIdAndUserId(taskId, userId)
        //        .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 直接根据任务ID查询任务，不验证用户ID
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 获取任务下的所有评估
        List<Evaluation> evaluations = evaluationRepository.findByTaskId(taskId);

        // 将评估实体转换为评估值对象
        List<EvaluationVO> evaluationVOList = evaluations.stream()
                .map(this::convertToEvaluationVO)
                .collect(Collectors.toList());

        // 创建并返回评估列表值对象
        return new EvaluationListVO(evaluationVOList);
    }

    /**
     * 查询评估状态
     *
     * @param taskId 任务ID
     * @param evalId 评估ID
     * @param userId 用户ID
     * @return 评估状态值对象
     */
    @Override
    public EvaluationStatusVO getEvaluationStatus(Integer taskId, Integer evalId, Integer userId) {
        // 验证任务是否存在且属于当前用户
        // taskRepository.findByIdAndUserId(taskId, userId)
        //        .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 直接根据任务ID查询任务，不验证用户ID
        taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));


        // 查询评估
        Evaluation evaluation = evaluationRepository.findByIdAndTaskId(evalId, taskId)
                .orElseThrow(() -> new EvaluationNotFoundException(evalId));

        // 根据评估状态返回不同的响应
        switch (evaluation.getStatus()) {
            case PENDING:
                // 等待中状态
                return EvaluationStatusVO.pending();

            case RUNNING:
                // 运行中状态，这里简化处理，实际应该有进度计算
                return EvaluationStatusVO.running(50);

            case COMPLETED:
                // 完成状态，返回结果
                return createCompletedResponse(evaluation);

            case FAILED:
                // 失败状态，返回错误信息
                return createFailedResponse(evaluation);

            default:
                // 未知状态，返回默认等待中状态
                return EvaluationStatusVO.pending();
        }
    }

    /**
     * 创建完成状态的响应
     *
     * @param evaluation 评估实体
     * @return 评估状态值对象
     */
    private EvaluationStatusVO createCompletedResponse(Evaluation evaluation) {
        try {
            // 解析参数和结果
            JsonNode parametersNode = objectMapper.readTree(evaluation.getParameters());
            JsonNode resultNode = evaluation.getResult() != null ?
                    objectMapper.readTree(evaluation.getResult()) : objectMapper.createObjectNode();

            // 创建结果对象
            ObjectNode resultObject = objectMapper.createObjectNode();

            switch (evaluation.getEvalType()) {
                case SINGLE_TURN:
                    // 单轮评估结果
                    resultObject.put("value", resultNode.asDouble());
                    resultObject.put("eval_type", "single_turn");
                    resultObject.put("metric", parametersNode.has("metric_id") ?
                            String.valueOf(parametersNode.get("metric_id").asInt()) : "unknown");
                    // 打印 resultObject 中所有键值对
                    System.out.println("resultObject 的 result值:" + resultObject.get("result"));
                    break;

                case CUSTOM:
                    // 自定义评估结果
                    resultObject.put("value", resultNode.asDouble());
                    resultObject.put("eval_type", "custom");
                    resultObject.put("metric", parametersNode.has("eval_metric") ?
                            parametersNode.get("eval_metric").asText() : "unknown");
                    if (parametersNode.has("custom_prompt")) {
                        resultObject.put("custom_prompt", parametersNode.get("custom_prompt").asText());
                    }
                    break;

                case MULTI_TURN:
                    // 多轮评估结果
                    resultObject.put("eval_type", "multi_turn");
                    resultObject.put("metric", parametersNode.has("eval_metric") ?
                            parametersNode.get("eval_metric").asText() : "unknown");

                    // 如果结果是数组，则处理每个对话的评估结果
                    if (resultNode.isArray()) {
                        double sum = 0;
                        int count = 0;

                        // 创建值数组
                        ObjectNode[] values = new ObjectNode[resultNode.size()];

                        for (int i = 0; i < resultNode.size(); i++) {
                            JsonNode item = resultNode.get(i);
                            ObjectNode valueObj = objectMapper.createObjectNode();
                            valueObj.put("conversation_id", i + 1);

                            // 假设结果中有一个指标名称字段，如coherence
                            String metricName = parametersNode.has("eval_metric") ?
                                    parametersNode.get("eval_metric").asText() : "value";

                            if (item.has(metricName)) {
                                double value = item.get(metricName).asDouble();
                                valueObj.put("value", value);
                                sum += value;
                                count++;
                            } else if (item.has("value")) {
                                double value = item.get("value").asDouble();
                                valueObj.put("value", value);
                                sum += value;
                                count++;
                            }

                            values[i] = valueObj;
                        }

                        // 计算平均值
                        double average = count > 0 ? sum / count : 0;

                        // 添加到结果对象
                        resultObject.putArray("values").addAll(List.of(values));
                        resultObject.put("average", average);
                    } else {
                        // 如果结果不是数组，则直接使用结果值
                        resultObject.put("value", resultNode.asDouble());
                    }
                    break;
            }

            return EvaluationStatusVO.completed(resultObject);

        } catch (Exception e) {
            // 解析异常时返回简化的结果
            return EvaluationStatusVO.completed(
                    Map.of(
                            "value", 0.0,
                            "eval_type", evaluation.getEvalType().getValue(),
                            "error", "Failed to parse result: " + e.getMessage()
                    )
            );
        }
    }

    /**
     * 创建失败状态的响应
     *
     * @param evaluation 评估实体
     * @return 评估状态值对象
     */
    private EvaluationStatusVO createFailedResponse(Evaluation evaluation) {
        try {
            // 尝试解析结果中的错误信息
            if (evaluation.getResult() != null) {
                JsonNode resultNode = objectMapper.readTree(evaluation.getResult());
                if (resultNode.has("error")) {
                    JsonNode errorNode = resultNode.get("error");
                    String code = errorNode.has("code") ? errorNode.get("code").asText() : "EVALUATION_ERROR";
                    String message = errorNode.has("message") ? errorNode.get("message").asText() : "评估过程中出现错误";
                    String details = errorNode.has("details") ? errorNode.get("details").asText() : null;

                    return EvaluationStatusVO.failed(code, message, details);
                }
            }
        } catch (Exception e) {
            // 忽略解析异常
        }

        // 默认错误信息
        return EvaluationStatusVO.failed(
                "EVALUATION_ERROR",
                "评估过程中出现错误",
                "无法获取详细错误信息"
        );
    }

    /**
     * 将评估实体转换为评估值对象
     *
     * @param evaluation 评估实体
     * @return 评估值对象
     */
    private EvaluationVO convertToEvaluationVO(Evaluation evaluation) {
        String metricName = "";

        try {
            // 从parameters中提取metric信息
            JsonNode parametersNode = objectMapper.readTree(evaluation.getParameters());

            switch (evaluation.getEvalType()) {
                case SINGLE_TURN:
                    // 对于单轮评估，从metric_id获取指标名称
                    // 这里简化处理，实际应该查询single_turn_metrics表
                    metricName = String.valueOf(parametersNode.get("metric_id").asInt());
                    break;
                case CUSTOM:
                    // 对于自定义评估，直接使用eval_metric
                    metricName = parametersNode.get("eval_metric").asText();
                    break;
                case MULTI_TURN:
                    // 对于多轮评估，直接使用eval_metric
                    metricName = parametersNode.get("eval_metric").asText();
                    break;
            }
        } catch (Exception e) {
            // 解析异常时使用默认值
            metricName = "unknown";
        }

        // 创建并返回评估值对象
        return new EvaluationVO(
                evaluation.getId().toString(),
                "Evaluation " + evaluation.getId(), // 这里简化处理，实际应该有评估名称
                evaluation.getEvalType().getValue(),
                evaluation.getStatus().getValue(),
                metricName
        );
    }

    /**
     * 创建自定义评估
     *
     * @param taskId 任务ID
     * @param requestVO 自定义评估请求值对象
     * @param userId 用户ID
     * @return 评估响应值对象
     */
    @Override
    public EvaluationResponseVO createCustomEvaluation(Integer taskId, CustomEvalRequestVO requestVO, Integer userId) {
        // 打印 eval_type
        System.out.println("Evaluating with CustomEvalRequestVO type: " + requestVO.getEval_type());
        System.out.println("Evaluation CustomEvalRequestVO parameters: " + requestVO.getCustom_prompt());

        try {
            // 如果使用 fromValue 方法来转换 eval_type
            EvaluationType evalType = EvaluationType.fromValue(requestVO.getEval_type());
            System.out.println("Converted evalType: " + evalType);
        } catch (IllegalArgumentException e) {
            // 捕获非法参数异常并打印
            System.err.println("Invalid eval_type received: " + requestVO.getEval_type());
        }

        // 验证任务是否存在且属于当前用户
        // Task task = taskRepository.findByIdAndUserId(taskId, userId)
        //        .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 直接根据任务ID查询任务，不验证用户ID
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));

        try {
            // 创建参数对象
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("eval_metric", requestVO.getEval_metric());
            parameters.put("custom_prompt", requestVO.getCustom_prompt());

            // 将样本转换为JSON字符串
            String samplesJson = objectMapper.writeValueAsString(requestVO.getSamples());

            // 创建评估实体
            Evaluation evaluation = new Evaluation();
            evaluation.setTaskId(taskId);
            evaluation.setEvalType(EvaluationType.CUSTOM);
            evaluation.setSamples(samplesJson);
            evaluation.setParameters(objectMapper.writeValueAsString(parameters));
            evaluation.setStatus(EvaluationStatus.PENDING);
            evaluation.setPrompt(requestVO.getCustom_prompt()); // 保存自定义提示作为prompt

            // 保存评估实体
            Evaluation savedEvaluation = evaluationRepository.save(evaluation);

            // 异步执行评估
//            System.out.println("Executing Python script with arguments: " + Arrays.toString(args));
            System.out.println("执行异步！");
            pythonEvaluator.executeCustomEvaluation(savedEvaluation);

            // 更新任务的prompt
            task.setPrompt(requestVO.getCustom_prompt());
            taskRepository.save(task);

            // 构建轮询URL（现在直接指向评估详情接口）
            String pollingUrl = ServletUriComponentsBuilder.fromCurrentContextPath()
                    .path("/api/rag/task/{taskId}/eval/{evalId}")
                    .buildAndExpand(taskId, savedEvaluation.getId())
                    .toUriString();

            // 返回评估响应
            return new EvaluationResponseVO("success", savedEvaluation.getId().toString(), pollingUrl);

        } catch (Exception e) {
            // 处理异常
            throw new RuntimeException("创建自定义评估失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建多轮评估
     *
     * @param taskId 任务ID
     * @param requestVO 多轮评估请求值对象
     * @param userId 用户ID
     * @return 评估响应值对象
     */
    @Override
    public EvaluationResponseVO createMultiTurnEvaluation(Integer taskId, MultiTurnEvalRequestVO requestVO, Integer userId) {
        // 打印 eval_type
        System.out.println("Evaluating with CustomEvalRequestVO type: " + requestVO.getEval_type());
        System.out.println("Evaluation CustomEvalRequestVO parameters: " + requestVO.getCustom_prompt());

        try {
            // 如果使用 fromValue 方法来转换 eval_type
            EvaluationType evalType = EvaluationType.fromValue(requestVO.getEval_type());
            System.out.println("Converted evalType: " + evalType);
        } catch (IllegalArgumentException e) {
            // 捕获非法参数异常并打印
            System.err.println("Invalid eval_type received: " + requestVO.getEval_type());
        }
        System.out.println("准备repository");
        // 直接根据任务ID查询任务，先不验证用户ID
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));
        System.out.println("准备i进入try");
        try {
            // 创建参数对象
            System.out.println("进入try");
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("eval_metric", requestVO.getEval_metric());
            parameters.put("custom_prompt", requestVO.getCustom_prompt());

            // 将样本转换为JSON字符串
            String samplesJson = objectMapper.writeValueAsString(requestVO.getSamples());

            // 创建评估实体
            Evaluation evaluation = new Evaluation();
            evaluation.setTaskId(taskId);
            evaluation.setEvalType(EvaluationType.MULTI_TURN);
            evaluation.setSamples(samplesJson);
            evaluation.setParameters(objectMapper.writeValueAsString(parameters));
            evaluation.setStatus(EvaluationStatus.PENDING);
            evaluation.setPrompt(requestVO.getCustom_prompt()); // 保存自定义提示作为prompt

            // 保存评估实体
            Evaluation savedEvaluation = evaluationRepository.save(evaluation);
            System.out.println("准备异步");
            // 异步执行评估
            pythonEvaluator.executeMultiTurnEvaluation(savedEvaluation);
            System.out.println("异步结束");
            // 更新任务的prompt
            task.setPrompt(requestVO.getCustom_prompt());
            taskRepository.save(task);

            // 构建轮询URL（现在直接指向评估详情接口）
            String pollingUrl = ServletUriComponentsBuilder.fromCurrentContextPath()
                    .path("/api/rag/task/{taskId}/eval/{evalId}")
                    .buildAndExpand(taskId, savedEvaluation.getId())
                    .toUriString();

            // 返回评估响应
            return new EvaluationResponseVO("success", savedEvaluation.getId().toString(), pollingUrl);

        } catch (Exception e) {
            // 处理异常
            throw new RuntimeException("创建多轮评估失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建单轮评估
     *
     * @param taskId 任务ID
     * @param requestVO 单轮评估请求值对象
     * @param userId 用户ID
     * @return 评估响应值对象
     */
    @Override
    public EvaluationResponseVO createSingleTurnEvaluation(Integer taskId, SingleTurnEvalRequestVO requestVO, Integer userId) {
        // 直接根据任务ID查询任务，不验证用户ID
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));

        try {
            // 创建参数对象
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("metric_id", requestVO.getMetric_id());

            // 将样本转换为JSON字符串
            String samplesJson = objectMapper.writeValueAsString(requestVO.getSamples());

            // 创建评估实体
            Evaluation evaluation = new Evaluation();
            evaluation.setTaskId(taskId);
            evaluation.setEvalType(EvaluationType.SINGLE_TURN);
            evaluation.setSamples(samplesJson);
            evaluation.setParameters(objectMapper.writeValueAsString(parameters));
            evaluation.setStatus(EvaluationStatus.PENDING);

            // 保存评估实体
            Evaluation savedEvaluation = evaluationRepository.save(evaluation);

            // 异步执行评估
            pythonEvaluator.executeSingleTurnEvaluation(savedEvaluation);

            // 构建轮询URL（现在直接指向评估详情接口）
            String pollingUrl = ServletUriComponentsBuilder.fromCurrentContextPath()
                    .path("/api/rag/task/{taskId}/eval/{evalId}")
                    .buildAndExpand(taskId, savedEvaluation.getId())
                    .toUriString();

            // 返回评估响应
            return new EvaluationResponseVO("success", savedEvaluation.getId().toString(), pollingUrl);

        } catch (Exception e) {
            // 处理异常
            throw new RuntimeException("创建单轮评估失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除评估
     *
     * @param taskId 任务ID
     * @param evalId 评估ID
     * @param userId 用户ID
     * @return 删除评估响应值对象
     */
    @Override
    public DeleteEvaluationResponseVO deleteEvaluation(Integer taskId, Integer evalId, Integer userId) {
        // 直接根据任务ID查询任务，不验证用户ID
        taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 查询评估
        Evaluation evaluation = evaluationRepository.findByIdAndTaskId(evalId, taskId)
                .orElseThrow(() -> new EvaluationNotFoundException(evalId));

        // 获取评估类型
        String evalType = evaluation.getEvalType().getValue();

        // 删除评估
        evaluationRepository.delete(evaluation);

        // 返回删除成功的响应
        return new DeleteEvaluationResponseVO("success", "evaluation deleted", evalType);
    }

    /**
     * 获取评估详情
     *
     * @param taskId 任务ID
     * @param evalId 评估ID
     * @param userId 用户ID
     * @return 评估详情值对象
     */
    @Override
    public EvaluationDetailVO getEvaluationDetail(Integer taskId, Integer evalId, Integer userId) {
        // 直接根据任务ID查询任务，不验证用户ID
        taskRepository.findById(taskId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 查询评估
        Evaluation evaluation = evaluationRepository.findByIdAndTaskId(evalId, taskId)
                .orElseThrow(() -> new EvaluationNotFoundException(evalId));

        try {
            // 创建评估详情值对象
            EvaluationDetailVO detailVO = new EvaluationDetailVO();
            detailVO.setId(evaluation.getId().toString());
            detailVO.setEval_type(evaluation.getEvalType().getValue());
            detailVO.setStatus(evaluation.getStatus().getValue());
            detailVO.setCreated_at(evaluation.getCreatedAt().toString());

            // 根据评估状态设置进度和错误信息
            switch (evaluation.getStatus()) {
                case PENDING:
                    // 等待中状态
                    detailVO.setProgress(0);
                    break;
                case RUNNING:
                    // 运行中状态，这里简化处理，实际应该有进度计算
                    detailVO.setProgress(50);
                    break;
                case COMPLETED:
                    // 完成状态
                    detailVO.setProgress(100);
                    break;
                case FAILED:
                    // 失败状态，设置错误信息
                    detailVO.setProgress(0);
                    // 尝试解析结果中的错误信息
                    if (evaluation.getResult() != null) {
                        try {
                            JsonNode resultNode = objectMapper.readTree(evaluation.getResult());
                            if (resultNode.has("error")) {
                                JsonNode errorNode = resultNode.get("error");
                                String code = errorNode.has("code") ? errorNode.get("code").asText() : "EVALUATION_ERROR";
                                String message = errorNode.has("message") ? errorNode.get("message").asText() : "评估过程中出现错误";
                                String details = errorNode.has("details") ? errorNode.get("details").asText() : null;
                                detailVO.setError(new EvaluationDetailVO.ErrorInfo(code, message, details));
                            }
                        } catch (Exception e) {
                            // 解析失败时设置默认错误信息
                            detailVO.setError(new EvaluationDetailVO.ErrorInfo("EVALUATION_ERROR", "评估失败", null));
                        }
                    } else {
                        // 没有结果时设置默认错误信息
                        detailVO.setError(new EvaluationDetailVO.ErrorInfo("EVALUATION_ERROR", "评估失败", null));
                    }
                    break;
                default:
                    // 未知状态，设置为等待中
                    detailVO.setProgress(0);
                    break;
            }

            // 根据评估类型处理样本和结果
            switch (evaluation.getEvalType()) {
                case SINGLE_TURN:
                    // 单轮评估，使用getSampleDict获取单个样本
                    detailVO.setSamples(objectMapper.readValue(evaluation.getSampleDict(), Object.class));

                    // 解析参数
                    JsonNode parametersNode = objectMapper.readTree(evaluation.getParameters());
                    Map<String, Object> singleTurnParams = new HashMap<>();
                    singleTurnParams.put("metric_id", parametersNode.get("metric_id").asInt());
                    detailVO.setParameters(singleTurnParams);

                    // 解析结果（如果有）
                    if (evaluation.getResult() != null) {
                        detailVO.setResult(objectMapper.readValue(evaluation.getResult(), Double.class));
                    }
                    break;

                case CUSTOM:
                    // 自定义评估，使用getSampleDict获取单个样本
                    detailVO.setSamples(objectMapper.readValue(evaluation.getSampleDict(), Object.class));

                    // 解析参数
                    JsonNode customParamsNode = objectMapper.readTree(evaluation.getParameters());
                    Map<String, Object> customParams = new HashMap<>();
                    customParams.put("eval_metric", customParamsNode.get("eval_metric").asText());
                    detailVO.setParameters(customParams);

                    // 解析结果（如果有）
                    if (evaluation.getResult() != null) {
                        detailVO.setResult(objectMapper.readValue(evaluation.getResult(), Double.class));
                    }
                    break;

                case MULTI_TURN:
                    // 多轮评估，使用getSamples获取所有样本
                    // 解析参数
                    JsonNode multiTurnParamsNode = objectMapper.readTree(evaluation.getParameters());
                    Map<String, Object> multiTurnParams = new HashMap<>();
                    multiTurnParams.put("eval_metric", multiTurnParamsNode.get("eval_metric").asText());
                    detailVO.setParameters(multiTurnParams);

                    // 解析结果（如果有）
                    if (evaluation.getResult() != null) {
                        // 对于多轮评估，结果是一个数组
                        JsonNode resultNode = objectMapper.readTree(evaluation.getResult());

                        // 将样本和结果结合
                        JsonNode samplesNode = objectMapper.readTree(evaluation.getSamples());
                        if (samplesNode.isArray() && resultNode.isArray() && samplesNode.size() == resultNode.size()) {
                            Object[] results = new Object[samplesNode.size()];
                            for (int i = 0; i < samplesNode.size(); i++) {
                                Map<String, Object> resultItem = new HashMap<>();
                                resultItem.put("user_input", samplesNode.get(i).get("user_input"));

                                // 添加评估指标结果
                                String metricName = multiTurnParamsNode.get("eval_metric").asText();
                                resultItem.put(metricName, resultNode.get(i).asDouble());

                                results[i] = resultItem;
                            }
                            detailVO.setResult(results);
                        } else {
                            // 如果样本和结果不匹配，直接返回结果
                            detailVO.setResult(objectMapper.readValue(evaluation.getResult(), Object.class));
                        }
                    }
                    break;
            }

            return detailVO;

        } catch (Exception e) {
            // 处理异常
            throw new RuntimeException("获取评估详情失败: " + e.getMessage(), e);
        }
    }
}
