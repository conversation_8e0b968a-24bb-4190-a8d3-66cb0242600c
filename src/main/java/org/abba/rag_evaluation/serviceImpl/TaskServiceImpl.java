package org.abba.rag_evaluation.serviceImpl;

import org.abba.rag_evaluation.exception.TaskNotFoundException;
import org.abba.rag_evaluation.po.Task;
import org.abba.rag_evaluation.repository.TaskRepository;
import org.abba.rag_evaluation.service.TaskService;
import org.abba.rag_evaluation.vo.CreateTaskRequestVO;
import org.abba.rag_evaluation.vo.TaskDetailResponseVO;
import org.abba.rag_evaluation.vo.TaskListVO;
import org.abba.rag_evaluation.vo.TaskListVO.TaskVO;
import org.abba.rag_evaluation.vo.TaskResponseVO;
import org.abba.rag_evaluation.vo.TaskResponseVO.TaskInfo;
import org.abba.rag_evaluation.vo.UpdateTaskRequestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.LineNumberReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务服务实现类
 */
@Service
public class TaskServiceImpl implements TaskService {

    /**
     * 任务数据访问层
     */
    private final TaskRepository taskRepository;

    /**
     * 日期格式化器
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 构造函数注入TaskRepository
     *
     * @param taskRepository 任务数据访问层
     */
    @Autowired
    public TaskServiceImpl(TaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    /**
     * 创建一个Hello World任务
     *
     * @return 创建的任务
     */
    @Override
    public Task createHelloWorldTask() {
        // 创建一个新的任务
        Task task = new Task("Hello World Task", "这是一个Hello World示例任务", 1);
        // 保存任务到数据库
        return taskRepository.save(task);
    }

    /**
     * 获取Hello World消息
     *
     * @return Hello World消息
     */
    @Override
    public String getHelloWorld(){
        try {
            String pythonPath = "/home/<USER>/.conda/envs/rag/bin/python";
            String scriptPath = "/home/<USER>/SEII_iter2/Rag_evaluation/src/main/resources/python_docs/1_evaluation.py";
            // 单论评估
//            String sampleJson = "{\"user_input\":\"什么是量子计算？\","
//                    + "\"response\":\"量子计算是利用量子力学原理进行计算的技术。\","
//                    + "\"retrieved_contexts\":[\"量子计算是一种新型计算技术...\",\"量子比特是量子计算的基本单位...\"],"
//                    + "\"reference\":\"量子计算是一种利用量子力学原理进行信息处理的计算技术。\"}";
//
//            String[] args = new String[] {
//                    pythonPath,
//                    scriptPath,
//                    "single_turn",
//                    sampleJson,
//                    "5"
//            };
            // 自定义评估
//            String sampleJson = "{\"user_input\":\"什么是量子计算？\","
//                    + "\"response\":\"量子计算是利用量子力学原理进行计算的技术。\","
//                    + "\"retrieved_contexts\":[\"量子计算是一种新型计算技术...\",\"量子比特是量子计算的基本单位...\"],"
//                    + "\"reference\":\"量子计算是一种利用量子力学原理进行信息处理的计算技术。\"}";
//
//            String[] args = new String[] {
//                    pythonPath,
//                    scriptPath,
//                    "custom",
//                    sampleJson,
//                    "clarity",
//                    "评估回答的艺术性"
//            };
            // 多轮评估
            String samplesJson = "["
                    + "{"
                    + "  \"user_input\": ["
                    + "    {\"type\":\"human\",\"content\":\"I need to increase my credit limit and check why my last transaction at Walmart was declined.\"},"
                    + "    {\"type\":\"ai\",\"content\":\"Let's check the declined transaction first, the $234.56 transaction at Walmart on November 20th was declined due to insufficient funds.\"},"
                    + "    {\"type\":\"human\",\"content\":\"That's not possible, I had enough money in my account.\"},"
                    + "    {\"type\":\"ai\",\"content\":\"I understand your concern. Let me check your account details...\"}"
                    + "  ]"
                    + "},"
                    + "{"
                    + "  \"user_input\": ["
                    + "    {\"type\":\"human\",\"content\":\"Hi there!\"},"
                    + "    {\"type\":\"ai\",\"content\":\"Hello! How can I help you today?\"},"
                    + "    {\"type\":\"human\",\"content\":\"How much money do I have in my bank account?\"},"
                    + "    {\"type\":\"ai\",\"content\":\"Your current balance is ₹85,750.\"}"
                    + "  ]"
                    + "}"
                    + "]";

            String[] args = new String[] {
                    pythonPath,
                    scriptPath,
                    "multi_turn",
                    samplesJson,
                    "coherence",
                    "评估对话连贯性"
            };
            Process process = Runtime.getRuntime().exec(args);
            //获取结果的同时设置输入流编码格式"gb2312"
            InputStreamReader isr = new InputStreamReader(process.getInputStream(),"UTF-8");
            LineNumberReader input = new LineNumberReader(isr);
            String result = "";
            result = input.readLine();
            System.out.println(result);
            input.close();
            isr.close();
            process.waitFor();
            return result;
        } catch (InterruptedException | IOException e) {
            System.out.println("调用python脚本并读取结果时出错：" + e.getMessage());
            return "调用python脚本并读取结果时出错：" + e.getMessage();
        }
    }

    /**
     * 获取所有任务列表
     *
     * @return 任务列表值对象
     */
    @Override
    public TaskListVO getAllTasks() {
        // 从数据库获取所有任务
        List<Task> tasks = taskRepository.findAll();

        // 将任务实体转换为任务值对象，包含描述和创建时间
        List<TaskVO> taskVOList = tasks.stream()
                .map(task -> new TaskVO(
                        task.getId().toString(),
                        task.getName(),
                        task.getDescription(),
                        task.getCreatedAt().format(DATE_FORMATTER)
                ))
                .collect(Collectors.toList());

        // 创建并返回任务列表值对象
        return new TaskListVO(taskVOList);
    }

    /**
     * 创建新任务
     *
     * @param requestVO 创建任务请求值对象
     * @param userId 用户ID
     * @return 任务响应值对象
     */
    @Override
    public TaskResponseVO createTask(CreateTaskRequestVO requestVO, Integer userId) {
        // 创建新任务
        Task task = new Task(requestVO.getName(), requestVO.getDescription(), userId);

        // 保存任务到数据库
        Task savedTask = taskRepository.save(task);

        // 创建任务信息，包含描述
        TaskInfo taskInfo = new TaskInfo(savedTask.getId().toString(), savedTask.getName(), savedTask.getDescription());

        // 返回成功响应
        return new TaskResponseVO("success", taskInfo);
    }

    /**
     * 更新任务信息
     *
     * @param taskId 任务ID
     * @param requestVO 更新任务请求值对象
     * @param userId 用户ID
     * @return 任务响应值对象
     */
    @Override
    public TaskResponseVO updateTask(Integer taskId, UpdateTaskRequestVO requestVO, Integer userId) {
        // 查找指定用户的指定任务
        Task task = taskRepository.findByIdAndUserId(taskId, userId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 更新任务信息
        task.setName(requestVO.getName());
        task.setDescription(requestVO.getDescription());

        // 保存更新后的任务
        Task updatedTask = taskRepository.save(task);

        // 创建任务信息，包含描述
        TaskInfo taskInfo = new TaskInfo(updatedTask.getId().toString(), updatedTask.getName(), updatedTask.getDescription());

        // 返回成功响应
        return new TaskResponseVO("success", taskInfo);
    }

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 任务响应值对象
     */
    @Override
    public TaskResponseVO deleteTask(Integer taskId, Integer userId) {
        // 查找指定用户的指定任务
        Task task = taskRepository.findByIdAndUserId(taskId, userId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 删除任务
        taskRepository.delete(task);

        // 返回成功响应
        return new TaskResponseVO("success", "task deleted");
    }

    /**
     * 查看任务详情
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 任务详情响应值对象
     */
    @Override
    public TaskDetailResponseVO getTaskDetail(Integer taskId, Integer userId) {
        // 查找指定用户的指定任务
        Task task = taskRepository.findByIdAndUserId(taskId, userId)
                .orElseThrow(() -> new TaskNotFoundException(taskId));

        // 格式化创建时间
        String formattedDate = task.getCreatedAt().format(DATE_FORMATTER);

        // 创建任务详情信息，包含描述
        TaskDetailResponseVO.TaskInfo taskInfo = new TaskDetailResponseVO.TaskInfo(
                task.getId().toString(),
                task.getName(),
                task.getDescription(),
                formattedDate
        );

        // 返回成功响应
        return new TaskDetailResponseVO("success", taskInfo);
    }
}