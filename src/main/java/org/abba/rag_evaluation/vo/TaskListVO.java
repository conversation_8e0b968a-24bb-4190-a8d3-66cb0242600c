package org.abba.rag_evaluation.vo;

import java.util.List;

/**
 * 任务列表值对象
 */
public class TaskListVO {

    /**
     * 任务列表
     */
    private List<TaskVO> tasks;

    /**
     * 默认构造函数
     */
    public TaskListVO() {
    }

    /**
     * 带参数的构造函数
     *
     * @param tasks 任务列表
     */
    public TaskListVO(List<TaskVO> tasks) {
        this.tasks = tasks;
    }

    /**
     * 获取任务列表
     *
     * @return 任务列表
     */
    public List<TaskVO> getTasks() {
        return tasks;
    }

    /**
     * 设置任务列表
     *
     * @param tasks 任务列表
     */
    public void setTasks(List<TaskVO> tasks) {
        this.tasks = tasks;
    }

    /**
     * 任务值对象，用于列表展示
     */
    public static class TaskVO {

        /**
         * 任务ID
         */
        private String id;

        /**
         * 任务名称
         */
        private String name;

        /**
         * 任务描述
         */
        private String description;

        /**
         * 创建时间
         */
        private String created_at;

        /**
         * 默认构造函数
         */
        public TaskVO() {
        }

        /**
         * 带参数的构造函数
         *
         * @param id 任务ID
         * @param name 任务名称
         */
        public TaskVO(String id, String name) {
            this.id = id;
            this.name = name;
        }

        /**
         * 带参数的构造函数（包含描述和创建时间）
         *
         * @param id 任务ID
         * @param name 任务名称
         * @param description 任务描述
         * @param created_at 创建时间
         */
        public TaskVO(String id, String name, String description, String created_at) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.created_at = created_at;
        }

        /**
         * 获取任务ID
         *
         * @return 任务ID
         */
        public String getId() {
            return id;
        }

        /**
         * 设置任务ID
         *
         * @param id 任务ID
         */
        public void setId(String id) {
            this.id = id;
        }

        /**
         * 获取任务名称
         *
         * @return 任务名称
         */
        public String getName() {
            return name;
        }

        /**
         * 设置任务名称
         *
         * @param name 任务名称
         */
        public void setName(String name) {
            this.name = name;
        }

        /**
         * 获取任务描述
         *
         * @return 任务描述
         */
        public String getDescription() {
            return description;
        }

        /**
         * 设置任务描述
         *
         * @param description 任务描述
         */
        public void setDescription(String description) {
            this.description = description;
        }

        /**
         * 获取创建时间
         *
         * @return 创建时间
         */
        public String getCreated_at() {
            return created_at;
        }

        /**
         * 设置创建时间
         *
         * @param created_at 创建时间
         */
        public void setCreated_at(String created_at) {
            this.created_at = created_at;
        }
    }
}
