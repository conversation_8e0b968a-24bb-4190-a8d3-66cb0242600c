package org.abba.rag_evaluation.vo;

/**
 * 任务响应值对象
 */
public class TaskResponseVO {

    /**
     * 状态
     */
    private String status;

    /**
     * 任务信息
     */
    private TaskInfo task;

    /**
     * 消息（用于删除操作）
     */
    private String message;

    /**
     * 默认构造函数
     */
    public TaskResponseVO() {
    }

    /**
     * 带参数的构造函数（用于创建和更新操作）
     *
     * @param status 状态
     * @param task 任务信息
     */
    public TaskResponseVO(String status, TaskInfo task) {
        this.status = status;
        this.task = task;
    }

    /**
     * 带参数的构造函数（用于删除操作）
     *
     * @param status 状态
     * @param message 消息
     */
    public TaskResponseVO(String status, String message) {
        this.status = status;
        this.message = message;
    }

    /**
     * 获取状态
     *
     * @return 状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取任务信息
     *
     * @return 任务信息
     */
    public TaskInfo getTask() {
        return task;
    }

    /**
     * 设置任务信息
     *
     * @param task 任务信息
     */
    public void setTask(TaskInfo task) {
        this.task = task;
    }

    /**
     * 获取消息
     *
     * @return 消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置消息
     *
     * @param message 消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 任务信息内部类
     */
    public static class TaskInfo {

        /**
         * 任务ID
         */
        private String id;

        /**
         * 任务名称
         */
        private String name;

        /**
         * 任务描述
         */
        private String description;

        /**
         * 任务日期（用于详情接口）
         */
        private String date;

        /**
         * 默认构造函数
         */
        public TaskInfo() {
        }

        /**
         * 带参数的构造函数
         *
         * @param id 任务ID
         * @param name 任务名称
         */
        public TaskInfo(String id, String name) {
            this.id = id;
            this.name = name;
        }

        /**
         * 带参数的构造函数（包含描述）
         *
         * @param id 任务ID
         * @param name 任务名称
         * @param description 任务描述
         */
        public TaskInfo(String id, String name, String description) {
            this.id = id;
            this.name = name;
            this.description = description;
        }

        /**
         * 带参数的构造函数（用于详情接口）
         *
         * @param id 任务ID
         * @param name 任务名称
         * @param date 任务日期
         */
        public TaskInfo(String id, String name, String date) {
            this.id = id;
            this.name = name;
            this.date = date;
        }

        /**
         * 获取任务ID
         *
         * @return 任务ID
         */
        public String getId() {
            return id;
        }

        /**
         * 设置任务ID
         *
         * @param id 任务ID
         */
        public void setId(String id) {
            this.id = id;
        }

        /**
         * 获取任务名称
         *
         * @return 任务名称
         */
        public String getName() {
            return name;
        }

        /**
         * 设置任务名称
         *
         * @param name 任务名称
         */
        public void setName(String name) {
            this.name = name;
        }

        /**
         * 获取任务描述
         *
         * @return 任务描述
         */
        public String getDescription() {
            return description;
        }

        /**
         * 设置任务描述
         *
         * @param description 任务描述
         */
        public void setDescription(String description) {
            this.description = description;
        }

        /**
         * 获取任务日期
         *
         * @return 任务日期
         */
        public String getDate() {
            return date;
        }

        /**
         * 设置任务日期
         *
         * @param date 任务日期
         */
        public void setDate(String date) {
            this.date = date;
        }
    }
}
