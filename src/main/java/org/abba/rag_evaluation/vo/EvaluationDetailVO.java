package org.abba.rag_evaluation.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;

import java.time.LocalDateTime;

/**
 * 评估详情值对象
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluationDetailVO {

    /**
     * 评估ID
     */
    private String id;

    /**
     * 评估类型
     */
    private String eval_type;

    /**
     * 评估状态
     */
    private String status;

    /**
     * 样本
     */
    private Object samples;

    /**
     * 参数
     */
    private Object parameters;

    /**
     * 结果
     */
    private Object result;

    /**
     * 创建时间
     */
    private String created_at;

    /**
     * 评估进度（0-100）
     */
    private Integer progress;

    /**
     * 错误信息（仅在状态为failed时有值）
     */
    private ErrorInfo error;

    /**
     * 默认构造函数
     */
    public EvaluationDetailVO() {
    }

    /**
     * 带参数的构造函数
     *
     * @param id 评估ID
     * @param eval_type 评估类型
     * @param status 评估状态
     * @param samples 样本
     * @param parameters 参数
     * @param result 结果
     * @param created_at 创建时间
     */
    public EvaluationDetailVO(String id, String eval_type, String status, Object samples, Object parameters, Object result, String created_at) {
        this.id = id;
        this.eval_type = eval_type;
        this.status = status;
        this.samples = samples;
        this.parameters = parameters;
        this.result = result;
        this.created_at = created_at;
    }

    /**
     * 获取评估ID
     *
     * @return 评估ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置评估ID
     *
     * @param id 评估ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取评估类型
     *
     * @return 评估类型
     */
    public String getEval_type() {
        return eval_type;
    }

    /**
     * 设置评估类型
     *
     * @param eval_type 评估类型
     */
    public void setEval_type(String eval_type) {
        this.eval_type = eval_type;
    }

    /**
     * 获取评估状态
     *
     * @return 评估状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置评估状态
     *
     * @param status 评估状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取样本
     *
     * @return 样本
     */
    public Object getSamples() {
        return samples;
    }

    /**
     * 设置样本
     *
     * @param samples 样本
     */
    public void setSamples(Object samples) {
        this.samples = samples;
    }

    /**
     * 获取参数
     *
     * @return 参数
     */
    public Object getParameters() {
        return parameters;
    }

    /**
     * 设置参数
     *
     * @param parameters 参数
     */
    public void setParameters(Object parameters) {
        this.parameters = parameters;
    }

    /**
     * 获取结果
     *
     * @return 结果
     */
    public Object getResult() {
        return result;
    }

    /**
     * 设置结果
     *
     * @param result 结果
     */
    public void setResult(Object result) {
        this.result = result;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public String getCreated_at() {
        return created_at;
    }

    /**
     * 设置创建时间
     *
     * @param created_at 创建时间
     */
    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    /**
     * 获取评估进度
     *
     * @return 评估进度
     */
    public Integer getProgress() {
        return progress;
    }

    /**
     * 设置评估进度
     *
     * @param progress 评估进度
     */
    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public ErrorInfo getError() {
        return error;
    }

    /**
     * 设置错误信息
     *
     * @param error 错误信息
     */
    public void setError(ErrorInfo error) {
        this.error = error;
    }

    /**
     * 错误信息内部类
     */
    public static class ErrorInfo {

        /**
         * 错误代码
         */
        private String code;

        /**
         * 错误消息
         */
        private String message;

        /**
         * 错误详情
         */
        private String details;

        /**
         * 默认构造函数
         */
        public ErrorInfo() {
        }

        /**
         * 带参数的构造函数
         *
         * @param code 错误代码
         * @param message 错误消息
         * @param details 错误详情
         */
        public ErrorInfo(String code, String message, String details) {
            this.code = code;
            this.message = message;
            this.details = details;
        }

        /**
         * 获取错误代码
         *
         * @return 错误代码
         */
        public String getCode() {
            return code;
        }

        /**
         * 设置错误代码
         *
         * @param code 错误代码
         */
        public void setCode(String code) {
            this.code = code;
        }

        /**
         * 获取错误消息
         *
         * @return 错误消息
         */
        public String getMessage() {
            return message;
        }

        /**
         * 设置错误消息
         *
         * @param message 错误消息
         */
        public void setMessage(String message) {
            this.message = message;
        }

        /**
         * 获取错误详情
         *
         * @return 错误详情
         */
        public String getDetails() {
            return details;
        }

        /**
         * 设置错误详情
         *
         * @param details 错误详情
         */
        public void setDetails(String details) {
            this.details = details;
        }
    }
}
