package org.abba.rag_evaluation.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.abba.rag_evaluation.service.EvaluationService;
import org.abba.rag_evaluation.vo.CustomEvalRequestVO;
import org.abba.rag_evaluation.vo.DeleteEvaluationResponseVO;
import org.abba.rag_evaluation.vo.EvaluationDetailVO;
import org.abba.rag_evaluation.vo.EvaluationListVO;
import org.abba.rag_evaluation.vo.EvaluationResponseVO;
import org.abba.rag_evaluation.vo.EvaluationStatusVO;
import org.abba.rag_evaluation.vo.MultiTurnEvalRequestVO;
import org.abba.rag_evaluation.vo.SingleTurnEvalRequestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 评估控制器
 */
@RestController
@RequestMapping("/api/rag/task/{taskId}/eval")
public class EvaluationController {

    /**
     * 评估服务
     */
    private final EvaluationService evaluationService;

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;

    /**
     * 模拟的当前用户ID
     */
    private static final Integer CURRENT_USER_ID = 1;

    /**
     * 构造函数注入
     *
     * @param evaluationService 评估服务
     */
    @Autowired
    public EvaluationController(EvaluationService evaluationService) {
        this.evaluationService = evaluationService;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 获取任务下的所有评估
     *
     * @param taskId 任务ID
     * @return 评估列表
     */
    @GetMapping
    public ResponseEntity<EvaluationListVO> getAllEvaluations(@PathVariable("taskId") Integer taskId) {
        // 获取任务下的所有评估
        EvaluationListVO evaluationList = evaluationService.getAllEvaluations(taskId, CURRENT_USER_ID);
        return ResponseEntity.ok(evaluationList);
    }

    // 注释：原有的status接口已经合并到评估详情接口中
    // 原有的 GET /{evalId}/status 接口已被 GET /{evalId} 接口替代

    /**
     * 创建评估
     *
     * @param taskId 任务ID
     * @param requestBody 请求体
     * @return 评估响应
     */
    @PostMapping
    public ResponseEntity<EvaluationResponseVO> createEvaluation(
            @PathVariable("taskId") Integer taskId,
            @RequestBody Map<String, Object> requestBody) {

        // 获取评估类型
        System.out.println("开始controller");
        String evalType = (String) requestBody.get("eval_type");
        System.out.println("Received request to create evaluation task for taskId: " + taskId + ", evalType: " + evalType);

        if ("custom".equals(evalType)) {
            System.out.println("识别为自定义评估");
            // 转换为自定义评估请求
            CustomEvalRequestVO requestVO = objectMapper.convertValue(requestBody, CustomEvalRequestVO.class);
            EvaluationResponseVO responseVO = evaluationService.createCustomEvaluation(taskId, requestVO, CURRENT_USER_ID);
            return ResponseEntity.ok(responseVO);
        } else if ("multi_turn".equals(evalType)) {
            // 转换为多轮评估请求
            System.out.println("识别为多轮评估");
            MultiTurnEvalRequestVO requestVO = objectMapper.convertValue(requestBody, MultiTurnEvalRequestVO.class);
            System.out.println("成功数据转换");
            EvaluationResponseVO responseVO = evaluationService.createMultiTurnEvaluation(taskId, requestVO, CURRENT_USER_ID);
            System.out.println("准备返回");
            return ResponseEntity.ok(responseVO);
        } else if ("single_turn".equals(evalType)) {
            // 转换为单轮评估请求
            System.out.println("识别为单轮评估");

            // 处理eval_metric到metric_id的转换（支持新版本接口）
            Map<String, Object> processedRequestBody = new HashMap<>(requestBody);
            if (processedRequestBody.containsKey("eval_metric") && !processedRequestBody.containsKey("metric_id")) {
                // 获取eval_metric并转换为对应的metric_id
                String evalMetric = (String) processedRequestBody.get("eval_metric");
                Integer metricId = convertEvalMetricToMetricId(evalMetric);

                // 添加metric_id字段并移除eval_metric字段
                processedRequestBody.put("metric_id", metricId);
                processedRequestBody.remove("eval_metric");

                System.out.println("将eval_metric " + evalMetric + " 转换为metric_id: " + metricId);
            } else if (processedRequestBody.containsKey("metric_id")) {
                // 兼容旧版本，直接使用metric_id
                System.out.println("使用旧版本metric_id: " + processedRequestBody.get("metric_id"));
            }

            SingleTurnEvalRequestVO requestVO = objectMapper.convertValue(processedRequestBody, SingleTurnEvalRequestVO.class);
            System.out.println("成功数据转换");
            EvaluationResponseVO responseVO = evaluationService.createSingleTurnEvaluation(taskId, requestVO, CURRENT_USER_ID);
            System.out.println("准备返回");
            return ResponseEntity.ok(responseVO);
        } else {
            throw new IllegalArgumentException("不支持的评估类型: " + evalType);
        }
    }

    /**
     * 删除评估
     *
     * @param taskId 任务ID
     * @param evalId 评估ID
     * @return 删除评估响应
     */
    @DeleteMapping("/{evalId}")
    public ResponseEntity<DeleteEvaluationResponseVO> deleteEvaluation(
            @PathVariable("taskId") Integer taskId,
            @PathVariable("evalId") Integer evalId) {
        // 删除评估
        DeleteEvaluationResponseVO responseVO = evaluationService.deleteEvaluation(taskId, evalId, CURRENT_USER_ID);
        return ResponseEntity.ok(responseVO);
    }

    /**
     * 查看评估详情
     *
     * @param taskId 任务ID
     * @param evalId 评估ID
     * @return 评估详情
     */
    @GetMapping("/{evalId}")
    public ResponseEntity<EvaluationDetailVO> getEvaluationDetail(
            @PathVariable("taskId") Integer taskId,
            @PathVariable("evalId") Integer evalId) {
        // 获取评估详情
        EvaluationDetailVO detailVO = evaluationService.getEvaluationDetail(taskId, evalId, CURRENT_USER_ID);
        return ResponseEntity.ok(detailVO);
    }

    /**
     * 将eval_metric转换为对应的metric_id
     * 用于支持新版本的单轮评估请求
     *
     * @param evalMetric 评估指标字符串
     * @return 对应的指标ID
     * @throws IllegalArgumentException 当evalMetric不在支持范围内时
     */
    private Integer convertEvalMetricToMetricId(String evalMetric) {
        // 根据映射关系转换eval_metric为metric_id
        switch (evalMetric) {
            case "CONTEXT_PRECISION":
                return 2;
            case "FAITHFULNESS":
                return 3;
            case "CONTEXT_RELEVANCE":
                return 4;
            case "FACTUAL_CORRECTNESS":
                return 5;
            default:
                throw new IllegalArgumentException("不支持的eval_metric: " + evalMetric + "。支持的值为: CONTEXT_PRECISION, FAITHFULNESS, CONTEXT_RELEVANCE, FACTUAL_CORRECTNESS");
        }
    }

    /**
     * 将metric_id转换为对应的eval_metric字符串
     * 用于兼容旧版本的单轮评估请求
     *
     * @param metricId 指标ID
     * @return 对应的评估指标字符串
     * @throws IllegalArgumentException 当metricId不在支持范围内时
     */
    private String convertMetricIdToEvalMetric(Integer metricId) {
        // 根据映射关系转换metric_id为eval_metric
        switch (metricId) {
            case 2:
                return "CONTEXT_PRECISION";
            case 3:
                return "FAITHFULNESS";
            case 4:
                return "CONTEXT_RELEVANCE";
            case 5:
                return "FACTUAL_CORRECTNESS";
            default:
                throw new IllegalArgumentException("不支持的metric_id: " + metricId + "。支持的值为: 2(CONTEXT_PRECISION), 3(FAITHFULNESS), 4(CONTEXT_RELEVANCE), 5(FACTUAL_CORRECTNESS)");
        }
    }
}
