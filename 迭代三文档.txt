@evalUrl = {{baseUrl}}/api/rag

### 1.1 获取所有任务列表
GET {{evalUrl}}/task HTTP/1.1

// todo need description, create time
# Response:
# {
#   "tasks": [
#     {
#       "id": "task1_id",
#       "name": "task1_name"
#     },
#     {
#       "id": "task2_id",
#       "name": "task2_name"
#     }
#   ]
# }

### 1.2 创建新任务
POST {{evalUrl}}/task HTTP/1.1
Content-Type: application/json

{
  "name": "task_name",
  "description": "optional_description"
}

// todo need description
# Response:
# {
#   "status": "success",
#   "task": {
#     "id": "new_task_id",
#     "name": "task_name"
#   }
# }

### 1.3 更新任务信息
PUT {{evalUrl}}/task/1 HTTP/1.1
Content-Type: application/json

{
  "name": "updated_task_name",
  "description": "updated_description"
}

// todo remove name
# Response:
# {
#   "status": "success",
#   "task": {
#     "id": "task_id",
#     "name": "updated_task_name"
#   }
# }

### 1.4 删除任务
DELETE {{evalUrl}}/task/2 HTTP/1.1

# Response:
# {
#   "status": "success",
#   "message": "task deleted"
# }

### 1.5 查看任务详情
GET {{evalUrl}}/task/1 HTTP/1.1

// todo add description, create time
# Response:
# {
#   "status": "success",
#   "task": {
#     "id": "task_id",
#     "name": "task_name",
#     "date": "task_date"
#   }
# }

############# 2. 评估任务相关接口 #############

### 2.1 获取任务下的所有评估
GET {{evalUrl}}/task/1/eval HTTP/1.1

# Response:
# {
#   "evaluations": [
#     {
#       "id": "eval1_id",
#       "name": "eval1_name",
#       "eval_type": "single_turn|custom|multi_turn",
#       "status": "completed",
#       "metric": "clarity"
#     },
#     {
#       "id": "eval2_id",
#       "name": "eval2_name",
#       "eval_type": "single_turn|custom|multi_turn",
#       "status": "in_progress",
#       "metric": "coherence"
#     }
#   ]
# }

### 2.2 创建评估任务 - 单轮评估 (metric_id: 5=FACTUAL_CORRECTNESS)
POST {{evalUrl}}/task/1/eval HTTP/1.1
Content-Type: application/json

// todo remove metric_id, add eval_metric
{
  "eval_type": "single_turn",
  "metric_id": 5,
  "samples": [
    {
      "user_input": "什么是量子计算？",
      "response": "量子计算是利用量子力学原理进行计算的技术。",
      "retrieved_contexts": ["量子计算是一种新型计算技术...", "量子比特是量子计算的基本单位..."],
      "reference": "量子计算是一种利用量子力学原理进行信息处理的计算技术。"
    },
    {
      "user_input": "人工智能的应用领域有哪些？",
      "response": "人工智能的应用领域包括自然语言处理、计算机视觉、机器人技术等。",
      "retrieved_contexts": ["人工智能在医疗、金融、教育等领域有广泛应用...", "自然语言处理是人工智能的重要分支..."],
      "reference": "人工智能的应用领域包括但不限于自然语言处理、计算机视觉、机器人技术、医疗诊断、金融分析、教育辅助等。"
    }
  ]
}

# Response:
# {
#   "status": "pending",
#   "eval_id": "eval_123",
#   "polling_url": "/api/rag/task/1/eval/eval_123/status"
# }

### 2.2 创建评估任务 - 自定义评估
POST {{evalUrl}}/task/1/eval HTTP/1.1
Content-Type: application/json

// todo remove custom_prompt, only use eval_metric
{
  "eval_type": "custom",
  "eval_metric": "clarity",
  "custom_prompt": "评估回答的清晰度",
  "samples": [
    {
      "user_input": "什么是量子计算？",
      "response": "量子计算是利用量子力学原理进行计算的技术。",
      "retrieved_contexts": ["量子计算是一种新型计算技术...", "量子比特是量子计算的基本单位..."],
      "reference": "量子计算是一种利用量子力学原理进行信息处理的计算技术。"
    }
  ]
}

# Response:
# {
#   "status": "pending",
#   "eval_id": "eval_123",
#   "polling_url": "/api/rag/task/1/eval/eval_123/status"
# }

### 2.2 创建评估任务 - 多轮评估
POST {{evalUrl}}/task/1/eval HTTP/1.1
Content-Type: application/json

// todo remove custom_prompt, only use eval_metric
{
  "eval_type": "multi_turn",
  "eval_metric": "coherence",
  "custom_prompt": "评估对话的连贯性",
  "samples": [
    {
      "user_input": [
        {"type": "human", "content": "我需要提高我的信用额度，并查看为什么我在沃尔玛的最后一笔交易被拒绝。"},
        {"type": "ai", "content": "让我们先检查被拒绝的交易，11月20日在沃尔玛的234.56美元交易因资金不足而被拒绝。"},
        {"type": "human", "content": "这不可能，我的账户里有足够的钱。"},
        {"type": "ai", "content": "我理解您的担忧。让我检查一下您的账户详情..."}
      ]
    },
    {
      "user_input": [
        {"type": "human", "content": "你好！"},
        {"type": "ai", "content": "你好！今天我能帮你什么忙？"},
        {"type": "human", "content": "我的银行账户里有多少钱？"},
        {"type": "ai", "content": "您当前的余额是85,750元。"}
      ]
    }
  ]
}

# Response:
# {
#   "status": "pending",
#   "eval_id": "eval_123",
#   "polling_url": "/api/rag/task/1/eval/eval_123/status"
# }

### 2.3 异步状态查询
GET {{evalUrl}}/task/1/eval/{{eval_id}}/status HTTP/1.1

# Response (pending):
# {
#   "status": "pending",
#   "progress": 0
# }

# Response (running):
# {
#   "status": "running",
#   "progress": 60
# }

# Response (completed - single_turn):
# {
#   "status": "completed",
#   "progress": 100,
#   "result": {
#     "value": 0.85,
#     "eval_type": "single_turn",
#     "metric": "accuracy"
#   }
# }

# Response (completed - custom):
# {
#   "status": "completed",
#   "progress": 100,
#   "result": {
#     "value": 0.92,
#     "eval_type": "custom",
#     "metric": "clarity",
#     "custom_prompt": "评估回答的艺术性"
#   }
# }

# Response (completed - multi_turn):
# {
#   "status": "completed",
#   "progress": 100,
#   "result": {
#     "eval_type": "multi_turn",
#     "metric": "coherence",
#     "values": [
#       {
#         "conversation_id": 1,
#         "value": 0.9
#       },
#       {
#         "conversation_id": 2,
#         "value": 0.85
#       }
#     ],
#     "average": 0.875
#   }
# }

# Response (failed):
# {
#   "status": "failed",
#   "progress": 0,
#   "error": {
#     "code": "EVALUATION_ERROR",
#     "message": "评估过程中出现错误",
#     "details": "Python脚本执行失败：无法解析输入样本"
#   }
# }

### 2.4 删除评估
// todo don't support delete eval, remove this API
DELETE {{evalUrl}}/task/1/eval/{{eval_id}} HTTP/1.1

# Response:
# {
#   "status": "success",
#   "message": "evaluation deleted",
#   "deleted_eval_type": "single_turn|custom|multi_turn"
# }

### 2.5 查看评估任务详情 - 单轮评估
// todo 请将这个api与2.3合并，使用GET /task/{task_id}/eval/{eval_id}获取评估详情
GET {{evalUrl}}/task/1/eval/2 HTTP/1.1

# Response (single_turn):
# {
#   "id": "eval_123",
#   "eval_type": "single_turn",
#   "status": "completed",
#   "samples": {
#     "user_input": "什么是量子计算？",
#     "response": "量子计算是利用量子力学原理进行计算的技术。",
#     "retrieved_contexts": ["上下文1", "上下文2"],
#     "reference": "标准答案文本"
#   },
#   "parameters": {
#     "metric_id": 5
#   },
#   "result": 0.85,
#   "created_at": "2023-10-05T08:30:00Z"
# }

# Response (custom):
# {
#   "id": "eval_123",
#   "eval_type": "custom",
#   "status": "completed",
#   "samples": {
#     "user_input": "什么是量子计算？",
#     "response": "量子计算是利用量子力学原理进行计算的技术。",
#     "retrieved_contexts": ["上下文1", "上下文2"],
#     "reference": "标准答案文本"
#   },
#   "parameters": {
#     "eval_metric": "clarity"
#   },
#   "result": 0.85,
#   "created_at": "2023-10-05T08:30:00Z"
# }

# Response (multi_turn):
# {
#   "id": "eval_456",
#   "eval_type": "multi_turn",
#   "status": "completed",
#   "parameters": {
#     "eval_metric": "coherence"
#   },
#   "result": [
#     {
#       "user_input": [
#         {"type": "human", "content": "对话内容1"},
#         {"type": "ai", "content": "回复内容1"}
#       ],
#       "coherence": 0.9
#     }
#   ],
#   "created_at": "2023-10-05T08:30:00Z"
# }

// todo hope support remove all tasks and evals
